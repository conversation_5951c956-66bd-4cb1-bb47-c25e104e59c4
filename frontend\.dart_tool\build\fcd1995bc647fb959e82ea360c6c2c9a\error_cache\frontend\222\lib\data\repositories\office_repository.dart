["drift_dev on lib/data/repositories/office_repository.dart", ["", "type 'Null' is not a subtype of type 'InterfaceElement' in type cast", "#0      new KnownDriftTypes._fromLibrary (package:drift_dev/src/analysis/resolver/dart/helper.dart:69:41)\n#1      KnownDriftTypes.resolve (package:drift_dev/src/analysis/resolver/dart/helper.dart:119:30)\n<asynchronous suspension>\n#2      DriftAnalysisDriver.knownTypes (package:drift_dev/src/analysis/driver/driver.dart:117:13)\n<asynchronous suspension>\n#3      DiscoverStep.discover (package:drift_dev/src/analysis/resolver/discover.dart:78:46)\n<asynchronous suspension>\n#4      DriftAnalysisDriver.discoverIfNecessary (package:drift_dev/src/analysis/driver/driver.dart:153:7)\n<asynchronous suspension>\n#5      DriftAnalysisDriver.findLocalElements (package:drift_dev/src/analysis/driver/driver.dart:190:7)\n<asynchronous suspension>\n#6      DriftDiscover.build (package:drift_dev/src/backends/build/analyzer.dart:36:22)\n<asynchronous suspension>\n#7      runBuilder.buildForInput (package:build/src/generate/run_builder.dart:83:7)\n<asynchronous suspension>\n#8      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)\n<asynchronous suspension>\n#9      scopeLogAsync.<anonymous closure> (package:build/src/builder/logging.dart:32:40)\n<asynchronous suspension>\n"]]